import { createApp, close, createHttpRequest } from '@midwayjs/mock';
import { Framework } from '@midwayjs/koa';
import { Application } from '@midwayjs/koa';

describe('test/response.test.ts', () => {
  let app: Application;
  let dbConnected = false;

  beforeAll(async () => {
    try {
      app = await createApp<Framework>();
      dbConnected = true;
      console.log('✅ 数据库连接成功，运行完整测试');
    } catch (err) {
      console.warn('⚠️ 数据库连接失败，跳过需要数据库的测试:', err.message);
      dbConnected = false;
    }
  }, 15000); // 增加超时时间到15秒

  afterAll(async () => {
    if (app) {
      await close(app);
    }
  });

  it('should POST /api/response - submit questionnaire response', async () => {
    if (!dbConnected) {
      console.log('⏭️ 跳过测试：数据库未连接');
      return;
    }

    // 测试提交问卷响应接口
    const submitData = {
      questionnaire_id: 1,
      parent_phone: '13800138000',
      parent_name: '张三家长',
      sso_student_id: 'student_001',
      month: '2024-01',
      school_rating: 5,
      school_description: '学校整体表现很好',
      teacher_evaluations: [
        {
          sso_teacher_id: 'teacher_001',
          rating: 5,
          description: '老师教学认真负责',
          tags: ['认真', '负责', '专业'],
          is_recommended: true,
          teaching_quality_rating: 5,
          teaching_attitude_rating: 5,
          classroom_management_rating: 4,
          communication_rating: 5,
          professional_knowledge_rating: 5,
          improvement_suggestions: '希望能多一些互动',
          most_satisfied_aspect: '教学方法很好',
          needs_improvement_aspect: '课堂互动可以更多'
        },
        {
          sso_teacher_id: 'teacher_002',
          rating: 4,
          description: '老师很有耐心',
          tags: ['耐心', '细心'],
          is_recommended: true,
          teaching_quality_rating: 4,
          teaching_attitude_rating: 5,
          classroom_management_rating: 4,
          communication_rating: 4,
          professional_knowledge_rating: 4
        }
      ],
      remarks: '整体评价很好'
    };

    const result = await createHttpRequest(app)
      .post('/api/response')
      .send(submitData);

    console.log('提交问卷响应结果:', result.body);
    expect(result.status).toBe(200);
    // 注意：由于没有真实的SSO验证，这个测试可能会失败
    // 这里主要是验证接口结构是否正确
  });

  it('should GET /api/response - get response list', async () => {
    if (!dbConnected) {
      console.log('⏭️ 跳过测试：数据库未连接');
      return;
    }

    // 测试获取响应列表接口
    const result = await createHttpRequest(app)
      .get('/api/response')
      .query({
        page: 1,
        limit: 10
      });

    console.log('获取响应列表结果:', result.body);
    expect(result.status).toBe(200);
  });

  it('should GET /api/response/check - check response exists', async () => {
    if (!dbConnected) {
      console.log('⏭️ 跳过测试：数据库未连接');
      return;
    }

    // 测试检查响应是否存在接口
    const result = await createHttpRequest(app)
      .get('/api/response/check')
      .query({
        parent_phone: '13800138000',
        questionnaire_id: 1,
        sso_student_id: 'student_001',
        month: '2024-01'
      });

    console.log('检查响应是否存在结果:', result.body);
    expect(result.status).toBe(200);
  });

  it('should GET /api/response/statistics/:questionnaireId - get questionnaire statistics', async () => {
    if (!dbConnected) {
      console.log('⏭️ 跳过测试：数据库未连接');
      return;
    }

    // 测试获取问卷统计信息接口
    const result = await createHttpRequest(app)
      .get('/api/response/statistics/1');

    console.log('获取问卷统计信息结果:', result.body);
    expect(result.status).toBe(200);
  });

  it('should GET /api/response/rating-info - get rating info', async () => {
    // 这个接口不需要数据库连接
    const result = await createHttpRequest(app)
      .get('/api/response/rating-info');

    console.log('获取评分信息结果:', result.body);
    expect(result.status).toBe(200);
    expect(result.body.errCode).toBe(0);
    expect(result.body.data).toHaveProperty('school_rating');
    expect(result.body.data).toHaveProperty('teacher_rating');
  });

  it('should GET /api/response/edit - get response for edit (page backfill)', async () => {
    if (!dbConnected) {
      console.log('⏭️ 跳过测试：数据库未连接');
      return;
    }

    // 测试获取问卷填写信息接口（用于页面回填）
    const result = await createHttpRequest(app)
      .get('/api/response/edit')
      .query({
        questionnaire_id: 1,
        parent_phone: '13800138000',
        sso_student_code: 'student_001',
        month: '2024-01'
      });

    console.log('获取问卷填写信息结果:', result.body);
    expect(result.status).toBe(200);
    expect(result.body.errCode).toBe(0);

    // 如果找到数据，验证返回结构
    if (result.body.data) {
      expect(result.body.data).toHaveProperty('response_id');
      expect(result.body.data).toHaveProperty('questionnaire_id');
      expect(result.body.data).toHaveProperty('parent_phone');
      expect(result.body.data).toHaveProperty('sso_student_code');
      expect(result.body.data).toHaveProperty('month');
      expect(result.body.data).toHaveProperty('teacher_evaluations');
      expect(Array.isArray(result.body.data.teacher_evaluations)).toBe(true);
    }
  });

  it('should validate DTO structure for response submission', () => {
    // 测试响应提交DTO结构是否正确
    const submitDto = {
      questionnaire_id: 1,
      parent_phone: '13800138000',
      sso_student_id: 'student_001',
      month: '2024-01',
      teacher_evaluations: [
        {
          sso_teacher_id: 'teacher_001',
          rating: 5
        }
      ]
    };

    expect(submitDto.questionnaire_id).toBeDefined();
    expect(submitDto.parent_phone).toMatch(/^1[3-9]\d{9}$/);
    expect(submitDto.month).toMatch(/^\d{4}-\d{2}$/);
    expect(submitDto.teacher_evaluations).toBeInstanceOf(Array);
    expect(submitDto.teacher_evaluations.length).toBeGreaterThan(0);
    expect(submitDto.teacher_evaluations[0].rating).toBeGreaterThanOrEqual(1);
    expect(submitDto.teacher_evaluations[0].rating).toBeLessThanOrEqual(10);
  });

  it('should validate star rating conversion logic', () => {
    // 测试星级评分转换逻辑
    const fiveStarConversions = [
      { star: 1, expectedScore: 20 },
      { star: 2, expectedScore: 40 },
      { star: 3, expectedScore: 60 },
      { star: 4, expectedScore: 80 },
      { star: 5, expectedScore: 100 }
    ];

    const tenStarConversions = [
      { star: 1, expectedScore: 10 },
      { star: 5, expectedScore: 50 },
      { star: 10, expectedScore: 100 }
    ];

    // 5星制转换验证
    fiveStarConversions.forEach(({ star, expectedScore }) => {
      const score = star * 20; // 5星模式：1星=20分
      expect(score).toBe(expectedScore);
    });

    // 10星制转换验证
    tenStarConversions.forEach(({ star, expectedScore }) => {
      const score = star * 10; // 10星模式：1星=10分
      expect(score).toBe(expectedScore);
    });
  });

  it('should validate unique key generation logic', () => {
    // 测试唯一键生成逻辑
    const parentPhone = '13800138000';
    const questionnaireId = 1;
    const studentId = 'student_001';
    const month = '2024-01';

    const uniqueKey = `${parentPhone}_${questionnaireId}_${studentId}_${month}`;
    const expectedKey = '13800138000_1_student_001_2024-01';

    expect(uniqueKey).toBe(expectedKey);
  });

  it('should validate average score calculation', () => {
    // 测试平均分计算逻辑
    const teacherEvaluations = [
      { rating: 5 }, // 5星 = 100分
      { rating: 4 }, // 4星 = 80分
      { rating: 3 }  // 3星 = 60分
    ];

    // 5星制计算
    const totalScore = teacherEvaluations.reduce((sum, evaluation) => {
      return sum + (evaluation.rating * 20); // 5星制转换
    }, 0);
    const averageScore = Math.round((totalScore / teacherEvaluations.length) * 100) / 100;

    expect(totalScore).toBe(240); // 100 + 80 + 60
    expect(averageScore).toBe(80); // 240 / 3
  });
});
