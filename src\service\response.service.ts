import { Provide, Inject } from '@midwayjs/core';
import { InjectRepository, InjectDataSource } from '@midwayjs/sequelize';
import { Repository } from 'sequelize-typescript';
import { Sequelize } from 'sequelize-typescript';
import { Transaction } from 'sequelize';
import { CustomError } from '../error/custom.error';
import { Response } from '../entity/response.entity';
import { Answer } from '../entity/answer.entity';
import { Questionnaire } from '../entity/questionnaire.entity';
import {
  SubmitResponseDTO,
  QueryResponseDTO,
  GetResponseForEditDTO,
} from '../dto/response.dto';
import { IQuestionnaireListResponse } from '../interface';
import { APIManager } from './api_sso/index.service';
import { Custome } from './api_sso/custome.service';

// SSO相关接口定义
interface ISSoStudentInfo {
  id: string;
  name: string;
  class: string;
  grade: string;
  school_code: string;
  status: string;
}

interface ISSoTeacherInfo {
  id: string;
  name: string;
  subject: string;
  position: string;
  department: string;
  school_code: string;
  status: string;
}

@Provide()
export class ResponseService {
  @InjectRepository(Response)
  responseRepository: Repository<Response>;

  @InjectRepository(Answer)
  answerRepository: Repository<Answer>;

  @InjectRepository(Questionnaire)
  questionnaireRepository: Repository<Questionnaire>;

  @Inject()
  apiManager: APIManager;

  @Inject()
  custome: Custome;

  @InjectDataSource()
  sequelize: Sequelize;

  /**
   * 验证SSO学生ID是否有效
   * @param enterpriseId 学校ID
   * @param ssoStudentCode SSO学生code
   * @param ssoStudentName SSO学生姓名
   * @returns 学生信息
   */
  private async validateSSOStudentId(
    enterpriseId: string,
    ssoStudentCode: string,
    ssoStudentName: string
  ): Promise<ISSoStudentInfo> {
    try {
      // 使用custome.service中的getStudentInfo方法
      const studentInfo = await this.custome.getStudentInfo(
        enterpriseId,
        ssoStudentCode,
        ssoStudentName
      );

      if (!studentInfo) {
        throw new CustomError('学生ID无效或学生不存在');
      }

      // 检查学生状态（如果API返回状态信息）
      if (studentInfo.status && studentInfo.status !== '在读') {
        throw new CustomError('学生状态异常');
      }

      return {
        id: studentInfo.id || ssoStudentCode,
        name: studentInfo.name,
        class: studentInfo.class?.name || studentInfo.class || '',
        grade: studentInfo.class?.grade?.name || studentInfo.grade || '',
        school_code: studentInfo.enterpriseId || enterpriseId,
        status: studentInfo.status || 'active',
      };
    } catch (error) {
      if (error instanceof CustomError) {
        throw error;
      }
      throw new CustomError('验证学生ID时发生错误：' + error.message);
    }
  }

  /**
   * 验证SSO教师ID是否有效
   * @param ssoTeacherId SSO教师ID
   * @param enterpriseCode 学校编码
   * @returns 教师信息
   */
  private async validateSSOTeacherId(
    ssoTeacherId: string,
    enterpriseCode: string
  ): Promise<ISSoTeacherInfo> {
    try {
      // 使用custome.service中的getMemberByCode方法
      const teacherInfo = await this.custome.getMemberByCode(
        enterpriseCode,
        ssoTeacherId
      );

      // console.log('teacherInfo', teacherInfo);
      if (!teacherInfo) {
        throw new CustomError(`教师ID ${ssoTeacherId} 无效或教师不存在`);
      }

      // 检查教师状态（如果API返回状态信息）
      if (
        teacherInfo.employment_status &&
        teacherInfo.employment_status !== '在岗'
      ) {
        throw new CustomError(`教师ID ${ssoTeacherId} 状态异常`);
      }

      return {
        id: teacherInfo.id,
        name: teacherInfo.name,
        subject: '', // 从memberType中获取学科信息
        position: '', // 职位信息
        department: '', // 部门信息
        school_code: enterpriseCode,
        status: teacherInfo.employment_status,
      };
    } catch (error) {
      if (error instanceof CustomError) {
        throw error;
      }
      throw new CustomError(
        `验证教师ID ${ssoTeacherId} 时发生错误：` + error.message
      );
    }
  }

  /**
   * 计算总平均分
   * @param teacherEvaluations 教师评价数组
   * @returns 总平均分（百分制）
   */
  private calculateTotalAverageScore(teacherEvaluations: any[]): number {
    if (!teacherEvaluations || teacherEvaluations.length === 0) {
      return 0;
    }

    const totalScore = teacherEvaluations.reduce((sum, evaluation) => {
      return sum + evaluation.rating; // 直接使用百分制评分
    }, 0);

    return Math.round((totalScore / teacherEvaluations.length) * 100) / 100; // 保留两位小数
  }

  /**
   * 从学生信息中提取年级编号和班级编号
   * @param studentInfo 学生信息
   * @returns 年级编号和班级编号
   */
  private extractGradeAndClassCodes(studentInfo: ISSoStudentInfo): {
    gradeCode: string | null;
    classCode: string | null;
  } {
    let gradeCode: string | null = null;
    let classCode: string | null = null;

    // 从年级信息中提取年级编号
    if (studentInfo.grade) {
      // 尝试从年级名称中提取数字编号
      const gradeMatch = studentInfo.grade.match(/(\d+)/);
      if (gradeMatch) {
        gradeCode = gradeMatch[1];
      } else {
        // 如果没有数字，尝试转换中文数字
        const chineseToNumber: { [key: string]: string } = {
          一: '1',
          二: '2',
          三: '3',
          四: '4',
          五: '5',
          六: '6',
          七: '7',
          八: '8',
          九: '9',
          十: '10',
        };

        for (const [chinese, number] of Object.entries(chineseToNumber)) {
          if (studentInfo.grade.includes(chinese)) {
            gradeCode = number;
            break;
          }
        }
      }
    }

    // 从班级信息中提取班级编号
    if (studentInfo.class) {
      // 尝试从班级名称中提取数字编号
      const classMatch = studentInfo.class.match(/(\d+)/);
      if (classMatch) {
        classCode = classMatch[1];
      } else {
        // 如果没有数字，尝试转换中文数字
        const chineseToNumber: { [key: string]: string } = {
          一: '1',
          二: '2',
          三: '3',
          四: '4',
          五: '5',
          六: '6',
          七: '7',
          八: '8',
          九: '9',
          十: '10',
        };

        for (const [chinese, number] of Object.entries(chineseToNumber)) {
          if (studentInfo.class.includes(chinese)) {
            classCode = number;
            break;
          }
        }
      }
    }

    return { gradeCode, classCode };
  }

  /**
   * 提交问卷响应
   * @param submitDto 提交响应DTO
   * @returns 创建的响应记录
   */
  async submitResponse(submitDto: SubmitResponseDTO): Promise<Response> {
    // 开启事务
    const transaction: Transaction = await this.sequelize.transaction();

    try {
      // 1. 验证问卷是否存在且可填写
      const questionnaire = await this.questionnaireRepository.findByPk(
        submitDto.questionnaire_id
      );
      if (!questionnaire) {
        throw new CustomError('问卷不存在');
      }

      if (questionnaire.status !== 'published') {
        throw new CustomError('问卷未发布，无法填写');
      }

      // 检查问卷时间范围
      const now = new Date();
      if (questionnaire.start_time && now < questionnaire.start_time) {
        throw new CustomError('问卷尚未开始');
      }
      if (questionnaire.end_time && now > questionnaire.end_time) {
        throw new CustomError('问卷已结束');
      }

      // 2. 验证月份是否匹配
      if (questionnaire.month !== submitDto.month) {
        throw new CustomError('提交的月份与问卷月份不匹配');
      }

      // 3. 检查是否已存在重复提交
      const existingResponse = await this.responseRepository.findOne({
        where: {
          parent_phone: submitDto.parent_phone,
          questionnaire_id: submitDto.questionnaire_id,
          sso_student_code: submitDto.sso_student_code,
          month: submitDto.month,
        },
        transaction,
      });

      if (existingResponse) {
        throw new CustomError('该家长已为此学生在本月提交过问卷，请勿重复提交');
      }

      // 4. 验证SSO学生ID
      // 从问卷信息中获取学校编码，然后查询学校ID
      let enterpriseId = '';
      try {
        const schoolInfo = await this.custome.getEnterpriseByCode(
          questionnaire.sso_school_code
        );
        enterpriseId = schoolInfo?.id || '';
      } catch (error) {
        throw new CustomError('无法获取学校信息：' + error.message);
      }

      const studentInfo = await this.validateSSOStudentId(
        enterpriseId,
        submitDto.sso_student_code,
        submitDto.sso_student_name
      );

      // 4.1. 提取年级编号和班级编号
      const { gradeCode, classCode } =
        this.extractGradeAndClassCodes(studentInfo);

      // 5. 验证所有教师ID并获取教师信息
      const teacherInfos: ISSoTeacherInfo[] = [];
      for (const evaluation of submitDto.teacher_evaluations) {
        const teacherInfo = await this.validateSSOTeacherId(
          evaluation.sso_teacher_id,
          questionnaire.sso_school_code
        );
        teacherInfos.push(teacherInfo);
      }

      // 6. 检查教师数量限制
      if (
        questionnaire.max_teachers_limit > 0 &&
        submitDto.teacher_evaluations.length > questionnaire.max_teachers_limit
      ) {
        throw new CustomError(
          `最多只能评价${questionnaire.max_teachers_limit}位教师`
        );
      }

      // 7. 验证评分范围（100分制）
      for (const evaluation of submitDto.teacher_evaluations) {
        if (evaluation.rating < 0 || evaluation.rating > 100) {
          throw new CustomError(`评分必须在0-100分之间`);
        }
      }

      // 8. 计算总平均分
      const totalAverageScore = this.calculateTotalAverageScore(
        submitDto.teacher_evaluations
      );

      // 9. 创建响应主记录
      const responseData = {
        questionnaire_id: submitDto.questionnaire_id,
        parent_phone: submitDto.parent_phone,
        parent_name: submitDto.parent_name,
        sso_student_code: submitDto.sso_student_code,
        sso_student_name: studentInfo.name,
        sso_student_class: studentInfo.class,
        sso_student_grade: studentInfo.grade,
        grade_code: gradeCode || submitDto.grade_code, // 优先使用提取的年级编号，否则使用提交的
        class_code: classCode || submitDto.class_code, // 优先使用提取的班级编号，否则使用提交的
        month: submitDto.month,
        school_rating: submitDto.school_rating,
        school_description: submitDto.school_description,
        total_average_score: totalAverageScore,
        teacher_count: submitDto.teacher_evaluations.length,
        is_completed: true,
        submission_ip: '', // 可以从请求中获取IP
        remarks: submitDto.remarks,
      };

      const response = await this.responseRepository.create(responseData, {
        transaction,
      });

      // 10. 批量创建答案记录
      const answerData = submitDto.teacher_evaluations.map(
        (evaluation, index) => {
          const teacherInfo = teacherInfos[index];
          return {
            response_id: response.id,
            sso_teacher_id: evaluation.sso_teacher_id,
            sso_teacher_name: teacherInfo.name,
            sso_teacher_subject: teacherInfo.subject,
            sso_teacher_position: teacherInfo.position,
            sso_teacher_department: teacherInfo.department,
            rating: evaluation.rating,
            description: evaluation.description,
          };
        }
      );

      await this.answerRepository.bulkCreate(answerData, { transaction });

      // 提交事务
      await transaction.commit();

      // 返回完整的响应记录（包含关联的答案）
      return await this.responseRepository.findByPk(response.id, {
        include: [
          {
            association: 'answers',
          },
        ],
      });
    } catch (error) {
      console.error('Error submitting response:', error);
      // 回滚事务
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * 查询响应列表
   * @param queryDto 查询条件DTO
   * @returns 响应列表和分页信息
   */
  async getResponseList(
    queryDto: QueryResponseDTO
  ): Promise<IQuestionnaireListResponse> {
    const {
      questionnaire_id,
      parent_phone,
      sso_student_code,
      month,
      grade_code,
      class_code,
      is_completed,
      page = 1,
      limit = 10,
    } = queryDto;

    // 构建查询条件
    const where: any = {};
    if (questionnaire_id) {
      where.questionnaire_id = questionnaire_id;
    }
    if (parent_phone) {
      where.parent_phone = parent_phone;
    }
    if (sso_student_code) {
      where.sso_student_coded = sso_student_code;
    }
    if (month) {
      where.month = month;
    }
    if (grade_code) {
      where.grade_code = grade_code;
    }
    if (class_code) {
      where.class_code = class_code;
    }
    if (typeof is_completed === 'boolean') {
      where.is_completed = is_completed;
    }

    // 计算分页参数
    const offset = (page - 1) * limit;

    // 查询数据
    const { rows: list, count: total } =
      await this.responseRepository.findAndCountAll({
        where,
        include: [
          {
            association: 'answers',
          },
        ],
        offset,
        limit,
        order: [['created_at', 'DESC']],
      });

    return {
      list,
      total,
      page,
      limit,
    };
  }

  /**
   * 根据ID获取响应详情
   * @param id 响应ID
   * @returns 响应详情
   */
  async getResponseById(id: number): Promise<Response> {
    const response = await this.responseRepository.findByPk(id, {
      include: [
        {
          association: 'answers',
        },
      ],
    });

    if (!response) {
      throw new CustomError('响应记录不存在');
    }

    return response;
  }

  /**
   * 检查是否已提交响应
   * @param parentPhone 家长手机号
   * @param questionnaireId 问卷ID
   * @param studentCode 学生Code
   * @param month 月份
   * @returns 是否已提交
   */
  async checkResponseExists(
    parentPhone: string,
    questionnaireId: number,
    studentCode: string,
    month: string
  ): Promise<boolean> {
    const existingResponse = await this.responseRepository.findOne({
      where: {
        parent_phone: parentPhone,
        questionnaire_id: questionnaireId,
        sso_student_code: studentCode,
        month: month,
      },
    });

    return !!existingResponse;
  }

  /**
   * 获取问卷统计信息
   * @param questionnaireId 问卷ID
   * @returns 统计信息
   */
  async getQuestionnaireStatistics(questionnaireId: number) {
    // 验证问卷是否存在
    const questionnaire = await this.questionnaireRepository.findByPk(
      questionnaireId
    );
    if (!questionnaire) {
      throw new CustomError('问卷不存在');
    }

    // 统计响应数量
    const totalResponses = await this.responseRepository.count({
      where: { questionnaire_id: questionnaireId },
    });

    const completedResponses = await this.responseRepository.count({
      where: {
        questionnaire_id: questionnaireId,
        is_completed: true,
      },
    });

    // 计算完成率
    const completionRate =
      totalResponses > 0
        ? Math.round((completedResponses / totalResponses) * 100 * 100) / 100
        : 0;

    // 计算平均评分
    const avgScoreResult = await this.responseRepository.findOne({
      where: { questionnaire_id: questionnaireId },
      attributes: [
        [
          this.sequelize.fn('AVG', this.sequelize.col('total_average_score')),
          'average_rating',
        ],
      ],
      raw: true,
    });

    const averageRating = avgScoreResult
      ? Math.round((avgScoreResult as any).average_rating * 100) / 100
      : 0;

    // 统计教师评价数量
    const teacherEvaluationCount = await this.answerRepository.count({
      include: [
        {
          association: 'response',
          where: { questionnaire_id: questionnaireId },
        },
      ],
    });

    return {
      questionnaire_id: questionnaireId,
      total_responses: totalResponses,
      completed_responses: completedResponses,
      completion_rate: completionRate,
      average_rating: averageRating,
      teacher_evaluation_count: teacherEvaluationCount,
    };
  }

  /**
   * 获取问卷填写信息（用于页面回填）
   * @param queryDto 查询条件DTO
   * @returns 问卷填写信息或null
   */
  async getResponseForEdit(
    queryDto: GetResponseForEditDTO
  ): Promise<Response | null> {
    const { questionnaire_id, parent_phone, sso_student_code, month } =
      queryDto;

    // 查询已填写的问卷响应
    const response = await this.responseRepository.findOne({
      where: {
        questionnaire_id,
        parent_phone,
        sso_student_code,
        month,
      },
      include: [
        {
          association: 'answers',
          // 按创建时间排序，确保数据顺序一致
          order: [['created_at', 'ASC']],
        },
        {
          association: 'questionnaire',
          attributes: ['id', 'title', 'month', 'star_rating_mode', 'status'],
        },
      ],
    });

    if (!response) {
      return null;
    }

    // 记录查询日志
    console.log('查询问卷填写信息成功', {
      questionnaire_id,
      parent_phone,
      sso_student_code,
      month,
      response_id: response.id,
      teacher_count: response.answers?.length || 0,
    });

    return response;
  }
}
