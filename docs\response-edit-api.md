# 问卷填写信息查询接口文档

## 接口概述

该接口用于查询已填写的问卷信息，主要用于页面回填功能。当用户需要查看或修改已提交的问卷时，可以通过此接口获取完整的填写数据。

## 接口详情

### 获取问卷填写信息

**接口地址：** `GET /api/response/edit`

**接口描述：** 根据查询条件获取已填写的问卷信息，用于页面回填

**请求参数：**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| questionnaire_id | number | 是 | 问卷ID |
| parent_phone | string | 是 | 家长手机号（格式：1[3-9]xxxxxxxxx） |
| sso_student_code | string | 是 | SSO学生code |
| month | string | 是 | 月份（格式：YYYY-MM） |

**请求示例：**
```
GET /api/response/edit?questionnaire_id=1&parent_phone=13800138000&sso_student_code=student_001&month=2024-01
```

**响应格式：**

**成功响应（找到数据）：**
```json
{
  "errCode": 0,
  "msg": "获取问卷填写信息成功",
  "data": {
    "response_id": 123,
    "questionnaire_id": 1,
    "questionnaire": {
      "id": 1,
      "title": "2024年1月教师评价问卷",
      "month": "2024-01",
      "star_rating_mode": 5,
      "status": "published"
    },
    "parent_phone": "13800138000",
    "parent_name": "张三家长",
    "sso_student_code": "student_001",
    "sso_student_name": "张小明",
    "sso_student_class": "三年级1班",
    "sso_student_grade": "三年级",
    "grade_code": "3",
    "class_code": "1",
    "month": "2024-01",
    "school_rating": 85,
    "school_description": "学校整体表现很好",
    "total_average_score": 88.5,
    "teacher_count": 2,
    "is_completed": true,
    "remarks": "整体评价很好",
    "teacher_evaluations": [
      {
        "sso_teacher_id": "teacher_001",
        "sso_teacher_name": "李老师",
        "sso_teacher_subject": "数学",
        "sso_teacher_position": "班主任",
        "sso_teacher_department": "数学组",
        "rating": 90,
        "description": "老师教学认真负责"
      },
      {
        "sso_teacher_id": "teacher_002",
        "sso_teacher_name": "王老师",
        "sso_teacher_subject": "语文",
        "sso_teacher_position": "任课教师",
        "sso_teacher_department": "语文组",
        "rating": 87,
        "description": "老师很有耐心"
      }
    ],
    "created_at": "2024-01-15T10:30:00.000Z",
    "updated_at": "2024-01-15T10:30:00.000Z"
  },
  "timestamp": 1705312200000
}
```

**成功响应（未找到数据）：**
```json
{
  "errCode": 0,
  "msg": "未找到问卷填写信息",
  "data": null,
  "timestamp": 1705312200000
}
```

**错误响应：**
```json
{
  "errCode": 400,
  "msg": "参数验证失败：家长手机号格式不正确",
  "data": null,
  "timestamp": 1705312200000
}
```

## 使用场景

1. **页面回填**：用户重新进入问卷填写页面时，自动填充已提交的数据
2. **数据查看**：用户查看自己已提交的问卷内容
3. **数据修改**：在允许修改的情况下，获取原始数据进行编辑

## 注意事项

1. **唯一性验证**：查询条件（家长手机号+问卷ID+学生code+月份）构成唯一键
2. **数据完整性**：返回的数据包含完整的问卷填写信息，包括学校评分和所有教师评价
3. **权限控制**：建议在实际使用中添加权限验证，确保只有相关用户可以查询自己的数据
4. **数据格式**：评分采用100分制，前端可根据问卷的星级模式进行转换显示

## 前端使用示例

```javascript
// 查询问卷填写信息
async function getResponseForEdit(params) {
  try {
    const response = await fetch(`/api/response/edit?${new URLSearchParams(params)}`);
    const result = await response.json();
    
    if (result.errCode === 0) {
      if (result.data) {
        // 找到数据，进行页面回填
        fillFormWithData(result.data);
      } else {
        // 未找到数据，显示空表单
        showEmptyForm();
      }
    } else {
      // 处理错误
      console.error('查询失败:', result.msg);
    }
  } catch (error) {
    console.error('请求失败:', error);
  }
}

// 使用示例
getResponseForEdit({
  questionnaire_id: 1,
  parent_phone: '13800138000',
  sso_student_code: 'student_001',
  month: '2024-01'
});
```

## 相关接口

- `POST /api/response` - 提交问卷响应
- `GET /api/response/check` - 检查是否已提交响应
- `GET /api/response` - 获取响应列表
- `GET /api/response/:id` - 根据ID获取响应详情
